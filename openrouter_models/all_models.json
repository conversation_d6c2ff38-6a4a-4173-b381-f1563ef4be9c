[{"id": "mistralai/mistral-medium-3", "name": "Mistral: Mistral Medium 3", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000004", "completion": "0.000002"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.5-pro-preview", "name": "Google: Gemini 2.5 Pro Preview", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0.00000125", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/caller-large", "name": "Arcee AI: <PERSON><PERSON>", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.00000055", "completion": "0.00000085"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/spotlight", "name": "Arcee AI: Spotlight", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.00000018", "completion": "0.00000018"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/maestro-reasoning", "name": "Arcee AI: <PERSON><PERSON> Reasoning", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000009", "completion": "0.0000033"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/virtuoso-large", "name": "Arcee AI: Virtuoso <PERSON>", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.00000075", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/coder-large", "name": "Arcee AI: <PERSON><PERSON>", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.0000005", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/virtuoso-medium-v2", "name": "Arcee AI: Virtuoso Medium V2", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000005", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "arcee-ai/arcee-blitz", "name": "Arcee AI: <PERSON><PERSON>", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.00000045", "completion": "0.00000075"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-4-reasoning-plus:free", "name": "Microsoft: Phi 4 Reasoning Plus (free)", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-4-reasoning-plus", "name": "Microsoft: Phi 4 Reasoning Plus", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.00000007", "completion": "0.00000035"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-4-reasoning:free", "name": "Microsoft: Phi 4 Reasoning (free)", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-0.6b-04-28:free", "name": "Qwen: Qwen3 0.6B (free)", "size": "6B", "context_length": 32000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "inception/mercury-coder-small-beta", "name": "Inception: Mercury Coder Small Beta", "size": "Unknown", "context_length": 32000, "pricing": {"prompt": "0.00000025", "completion": "0.000001"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-1.7b:free", "name": "Qwen: <PERSON>wen3 1.7B (free)", "size": "7B", "context_length": 32000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-4b:free", "name": "Qwen: <PERSON>wen3 4B (free)", "size": "4B", "context_length": 128000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "opengvlab/internvl3-14b:free", "name": "OpenGVLab: InternVL3 14B (free)", "size": "14B", "context_length": 32000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "opengvlab/internvl3-2b:free", "name": "OpenGVLab: InternVL3 2B (free)", "size": "2B", "context_length": 32000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-prover-v2:free", "name": "DeepSeek: DeepSeek Prover V2 (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-prover-v2", "name": "DeepSeek: DeepSeek Prover V2", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000005", "completion": "0.00000218"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-guard-4-12b", "name": "Meta: Llama Guard 4 12B", "size": "12B", "context_length": 163840, "pricing": {"prompt": "0.00000005", "completion": "0.00000005"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-30b-a3b:free", "name": "Qwen: Qwen3 30B A3B (free)", "size": "30B", "context_length": 40960, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-30b-a3b", "name": "Qwen: Qwen3 30B A3B", "size": "30B", "context_length": 40960, "pricing": {"prompt": "0.0000001", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-8b:free", "name": "Qwen: <PERSON>wen3 8B (free)", "size": "8B", "context_length": 40960, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-8b", "name": "Qwen: <PERSON>wen3 8B", "size": "8B", "context_length": 128000, "pricing": {"prompt": "0.000000035", "completion": "0.000000138"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-14b:free", "name": "Qwen: <PERSON>wen3 14B (free)", "size": "14B", "context_length": 40960, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-14b", "name": "Qwen: <PERSON>wen3 14B", "size": "14B", "context_length": 40960, "pricing": {"prompt": "0.00000007", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-32b:free", "name": "Qwen: <PERSON>wen3 32B (free)", "size": "32B", "context_length": 40960, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-32b", "name": "Qwen: <PERSON>wen3 32B", "size": "32B", "context_length": 40960, "pricing": {"prompt": "0.0000001", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-235b-a22b:free", "name": "Qwen: Qwen3 235B A22B (free)", "size": "235B", "context_length": 40960, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen3-235b-a22b", "name": "Qwen: Qwen3 235B A22B", "size": "235B", "context_length": 40960, "pricing": {"prompt": "0.00000015", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "tngtech/deepseek-r1t-chimera:free", "name": "TNG: DeepSeek R1T Chimera (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-z1-rumination-32b", "name": "THUDM: GLM Z1 Rumination 32B ", "size": "32B", "context_length": 32000, "pricing": {"prompt": "0.00000024", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-z1-9b:free", "name": "THUDM: GLM Z1 9B (free)", "size": "9B", "context_length": 32000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-4-9b:free", "name": "THUDM: GLM 4 9B (free)", "size": "9B", "context_length": 32000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "microsoft/mai-ds-r1:free", "name": "Microsoft: MAI DS R1 (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-z1-32b:free", "name": "THUDM: GLM Z1 32B (free)", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-z1-32b", "name": "THUDM: GLM Z1 32B", "size": "32B", "context_length": 32000, "pricing": {"prompt": "0.00000024", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-4-32b:free", "name": "THUDM: GLM 4 32B (free)", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "thudm/glm-4-32b", "name": "THUDM: GLM 4 32B", "size": "32B", "context_length": 32000, "pricing": {"prompt": "0.00000024", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.5-flash-preview", "name": "Google: Gemini 2.5 Flash Preview", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0.00000015", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.5-flash-preview:thinking", "name": "Google: Gemini 2.5 Flash Preview (thinking)", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0.00000015", "completion": "0.0000035"}, "free": false, "free_tokens": 0}, {"id": "openai/o4-mini-high", "name": "OpenAI: o4 Mini High", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000011", "completion": "0.0000044"}, "free": false, "free_tokens": 0}, {"id": "openai/o3", "name": "OpenAI: o3", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.00001", "completion": "0.00004"}, "free": false, "free_tokens": 0}, {"id": "openai/o4-mini", "name": "OpenAI: o4 Mini", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000011", "completion": "0.0000044"}, "free": false, "free_tokens": 0}, {"id": "shisa-ai/shisa-v2-llama3.3-70b:free", "name": "Shisa AI: Shisa V2 Llama 3.3 70B  (free)", "size": "70B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen2.5-coder-7b-instruct", "name": "Qwen: Qwen2.5 Coder 7B Instruct", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.00000001", "completion": "0.00000003"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4.1", "name": "OpenAI: GPT-4.1", "size": "Unknown", "context_length": 1047576, "pricing": {"prompt": "0.000002", "completion": "0.000008"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4.1-mini", "name": "OpenAI: GPT-4.1 Mini", "size": "Unknown", "context_length": 1047576, "pricing": {"prompt": "0.0000004", "completion": "0.0000016"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4.1-nano", "name": "OpenAI: GPT-4.1 Nano", "size": "Unknown", "context_length": 1047576, "pricing": {"prompt": "0.0000001", "completion": "0.0000004"}, "free": false, "free_tokens": 0}, {"id": "eleutherai/llemma_7b", "name": "EleutherAI: Llemma 7b", "size": "7B", "context_length": 4096, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "alfredpros/codellama-7b-instruct-solidity", "name": "AlfredPros: CodeLLaMa 7B Instruct Solidity", "size": "7B", "context_length": 4096, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "arliai/qwq-32b-arliai-rpr-v1:free", "name": "ArliAI: QwQ 32B RpR v1 (free)", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "agentica-org/deepcoder-14b-preview:free", "name": "Agentica: Deepcoder 14B Preview (free)", "size": "14B", "context_length": 96000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "moonshotai/kimi-vl-a3b-thinking:free", "name": "Moonshot AI: <PERSON><PERSON> VL A3B Thinking (free)", "size": "3B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "x-ai/grok-3-mini-beta", "name": "xAI: Grok 3 Mini Beta", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000003", "completion": "0.0000005"}, "free": false, "free_tokens": 0}, {"id": "x-ai/grok-3-beta", "name": "xAI: Grok 3 Beta", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1:free", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1 (free)", "size": "9B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1", "size": "9B", "context_length": 131072, "pricing": {"prompt": "0.00000013", "completion": "0.0000004"}, "free": false, "free_tokens": 0}, {"id": "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", "name": "NVIDIA: Llama 3.1 Nemotron Ultra 253B v1 (free)", "size": "253B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-4-maverick:free", "name": "Meta: Llama 4 Maverick (free)", "size": "Unknown", "context_length": 256000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-4-maverick", "name": "Meta: Llama 4 Maverick", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0.00000017", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-4-scout:free", "name": "Meta: Llama 4 Scout (free)", "size": "Unknown", "context_length": 512000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-4-scout", "name": "Meta: Llama 4 Scout", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0.00000008", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "all-hands/openhands-lm-32b-v0.1", "name": "OpenHands LM 32B V0.1", "size": "32B", "context_length": 16384, "pricing": {"prompt": "0.0000026", "completion": "0.0000034"}, "free": false, "free_tokens": 0}, {"id": "mistral/ministral-8b", "name": "Mistral: Ministral 8B", "size": "8B", "context_length": 131072, "pricing": {"prompt": "0.0000001", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-v3-base:free", "name": "DeepSeek: DeepSeek V3 Base (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "scb10x/llama3.1-typhoon2-8b-instruct", "name": "Typhoon2 8B Instruct", "size": "8B", "context_length": 8192, "pricing": {"prompt": "0.00000018", "completion": "0.00000018"}, "free": false, "free_tokens": 0}, {"id": "scb10x/llama3.1-typhoon2-70b-instruct", "name": "Typhoon2 70B Instruct", "size": "70B", "context_length": 8192, "pricing": {"prompt": "0.00000088", "completion": "0.00000088"}, "free": false, "free_tokens": 0}, {"id": "allenai/molmo-7b-d:free", "name": "AllenAI: Molmo 7B D (free)", "size": "7B", "context_length": 4096, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "bytedance-research/ui-tars-72b:free", "name": "Bytedance: UI-TARS 72B  (free)", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen2.5-vl-3b-instruct:free", "name": "Qwen: Qwen2.5 VL 3B Instruct (free)", "size": "3B", "context_length": 64000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.5-pro-exp-03-25", "name": "Google: Gemini 2.5 Pro Experimental", "size": "Unknown", "context_length": 1000000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen2.5-vl-32b-instruct:free", "name": "Qwen: Qwen2.5 VL 32B Instruct (free)", "size": "32B", "context_length": 8192, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen2.5-vl-32b-instruct", "name": "Qwen: Qwen2.5 VL 32B Instruct", "size": "32B", "context_length": 128000, "pricing": {"prompt": "0.0000009", "completion": "0.0000009"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-chat-v3-0324:free", "name": "DeepSeek: DeepSeek V3 0324 (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-chat-v3-0324", "name": "DeepSeek: DeepSeek V3 0324", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0.0000003", "completion": "0.00000088"}, "free": false, "free_tokens": 0}, {"id": "featherless/qwerky-72b:free", "name": "Qwerky 72B (free)", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "openai/o1-pro", "name": "OpenAI: o1-pro", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.00015", "completion": "0.0006"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-small-3.1-24b-instruct:free", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3.1 24B (free)", "size": "24B", "context_length": 96000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-small-3.1-24b-instruct", "name": "Mistral: Mistra<PERSON> Small 3.1 24B", "size": "24B", "context_length": 131072, "pricing": {"prompt": "0.00000005", "completion": "0.00000015"}, "free": false, "free_tokens": 0}, {"id": "open-r1/olympiccoder-32b:free", "name": "OlympicCoder 32B (free)", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-1b-it:free", "name": "Google: Gemma 3 1B (free)", "size": "1B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-4b-it:free", "name": "Google: Gemma 3 4B (free)", "size": "4B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-4b-it", "name": "Google: Gemma 3 4B", "size": "4B", "context_length": 131072, "pricing": {"prompt": "0.00000002", "completion": "0.00000004"}, "free": false, "free_tokens": 0}, {"id": "ai21/jamba-1.6-large", "name": "AI21: Jamba 1.6 Large", "size": "Unknown", "context_length": 256000, "pricing": {"prompt": "0.000002", "completion": "0.000008"}, "free": false, "free_tokens": 0}, {"id": "ai21/jamba-1.6-mini", "name": "AI21: Jamba Mini 1.6", "size": "Unknown", "context_length": 256000, "pricing": {"prompt": "0.0000002", "completion": "0.0000004"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-12b-it:free", "name": "Google: Gemma 3 12B (free)", "size": "12B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-12b-it", "name": "Google: Gemma 3 12B", "size": "12B", "context_length": 131072, "pricing": {"prompt": "0.00000005", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-a", "name": "Cohere: Command A", "size": "Unknown", "context_length": 256000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-mini-search-preview", "name": "OpenAI: GPT-4o-mini Search Preview", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00000015", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-search-preview", "name": "OpenAI: GPT-4o Search Preview", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "rekaai/reka-flash-3:free", "name": "Reka: <PERSON> 3 (free)", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-27b-it:free", "name": "Google: <PERSON> 3 27B (free)", "size": "7B", "context_length": 96000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-3-27b-it", "name": "Google: Gemma 3 27B", "size": "7B", "context_length": 131072, "pricing": {"prompt": "0.0000001", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "thedrummer/anubis-pro-105b-v1", "name": "TheDrummer: Anubis Pro 105B V1", "size": "5B", "context_length": 131072, "pricing": {"prompt": "0.0000008", "completion": "0.000001"}, "free": false, "free_tokens": 0}, {"id": "thedrummer/skyfall-36b-v2", "name": "TheDrummer: Skyfall 36B V2", "size": "6B", "context_length": 32768, "pricing": {"prompt": "0.0000005", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-4-multimodal-instruct", "name": "Microsoft: Phi 4 Multimodal Instruct", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.00000005", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "perplexity/sonar-reasoning-pro", "name": "Perplexity: Sonar Reasoning Pro", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000002", "completion": "0.000008"}, "free": false, "free_tokens": 0}, {"id": "perplexity/sonar-pro", "name": "Perplexity: Sonar Pro", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "perplexity/sonar-deep-research", "name": "Perplexity: Sonar Deep Research", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000002", "completion": "0.000008"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-zero:free", "name": "DeepSeek: DeepSeek R1 Zero (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwq-32b:free", "name": "Qwen: QwQ 32B (free)", "size": "32B", "context_length": 40000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwq-32b", "name": "Qwen: QwQ 32B", "size": "32B", "context_length": 131072, "pricing": {"prompt": "0.00000015", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "moonshotai/moonlight-16b-a3b-instruct:free", "name": "Moonshot AI: Moonlight 16B A3B Instruct (free)", "size": "16B", "context_length": 8192, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "nousresearch/deephermes-3-llama-3-8b-preview:free", "name": "Nous: DeepHermes 3 Llama 3 8B Preview (free)", "size": "8B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4.5-preview", "name": "OpenAI: GPT-4.5 (Preview)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000075", "completion": "0.00015"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.0-flash-lite-001", "name": "Google: Gemini 2.0 Flash Lite", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0.000000075", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.7-sonnet", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON>", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.7-sonnet:thinking", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (thinking)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.7-sonnet:beta", "name": "Anthropic: <PERSON> 3.7 <PERSON><PERSON> (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "perplexity/r1-1776", "name": "Perplexity: R1 1776", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000002", "completion": "0.000008"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-saba", "name": "Mistral: Sa<PERSON>", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.0000002", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "cognitivecomputations/dolphin3.0-r1-mistral-24b:free", "name": "Dolphin3.0 R1 Mistral 24B (free)", "size": "24B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "cognitivecomputations/dolphin3.0-mistral-24b:free", "name": "Dolphin3.0 Mistral 24B (free)", "size": "24B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-guard-3-8b", "name": "Llama Guard 3 8B", "size": "8B", "context_length": 131072, "pricing": {"prompt": "0.00000002", "completion": "0.00000006"}, "free": false, "free_tokens": 0}, {"id": "openai/o3-mini-high", "name": "OpenAI: o3 Mini High", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000011", "completion": "0.0000044"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-llama-8b", "name": "DeepSeek: R1 Distill Llama 8B", "size": "8B", "context_length": 32000, "pricing": {"prompt": "0.00000004", "completion": "0.00000004"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.0-flash-001", "name": "Google: Gemini 2.0 Flash", "size": "Unknown", "context_length": 1000000, "pricing": {"prompt": "0.0000001", "completion": "0.0000004"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-vl-plus", "name": "Qwen: Qwen VL Plus", "size": "Unknown", "context_length": 7500, "pricing": {"prompt": "0.00000021", "completion": "0.00000063"}, "free": false, "free_tokens": 0}, {"id": "aion-labs/aion-1.0", "name": "AionLabs: Aion-1.0", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000004", "completion": "0.000008"}, "free": false, "free_tokens": 0}, {"id": "aion-labs/aion-1.0-mini", "name": "AionLabs: Aion-1.0-Mini", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000007", "completion": "0.0000014"}, "free": false, "free_tokens": 0}, {"id": "aion-labs/aion-rp-llama-3.1-8b", "name": "AionLabs: Aion-RP 1.0 (8B)", "size": "8B", "context_length": 32768, "pricing": {"prompt": "0.0000002", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-vl-max", "name": "<PERSON>wen: <PERSON><PERSON> VL Max", "size": "Unknown", "context_length": 7500, "pricing": {"prompt": "0.0000008", "completion": "0.0000032"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-turbo", "name": "Qwen: <PERSON><PERSON>-<PERSON>", "size": "Unknown", "context_length": 1000000, "pricing": {"prompt": "0.00000005", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen2.5-vl-72b-instruct:free", "name": "Qwen: Qwen2.5 VL 72B Instruct (free)", "size": "72B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen2.5-vl-72b-instruct", "name": "Qwen: Qwen2.5 VL 72B Instruct", "size": "72B", "context_length": 32000, "pricing": {"prompt": "0.00000025", "completion": "0.00000075"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-plus", "name": "<PERSON>wen: <PERSON><PERSON>-Plus", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000004", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-max", "name": "<PERSON><PERSON>: <PERSON><PERSON>-<PERSON> ", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.0000016", "completion": "0.0000064"}, "free": false, "free_tokens": 0}, {"id": "openai/o3-mini", "name": "OpenAI: o3 Mini", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000011", "completion": "0.0000044"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-qwen-1.5b", "name": "DeepSeek: R1 <PERSON><PERSON><PERSON> 1.5B", "size": "5B", "context_length": 131072, "pricing": {"prompt": "0.00000018", "completion": "0.00000018"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-small-24b-instruct-2501:free", "name": "Mistral: <PERSON><PERSON><PERSON> Small 3 (free)", "size": "24B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-small-24b-instruct-2501", "name": "Mistral: Mistral Small 3", "size": "24B", "context_length": 28000, "pricing": {"prompt": "0.00000006", "completion": "0.00000012"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-qwen-32b:free", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 32B (free)", "size": "32B", "context_length": 16000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-qwen-32b", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 32B", "size": "32B", "context_length": 131072, "pricing": {"prompt": "0.00000012", "completion": "0.00000018"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-qwen-14b:free", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 14B (free)", "size": "14B", "context_length": 64000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-qwen-14b", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 14B", "size": "14B", "context_length": 64000, "pricing": {"prompt": "0.00000015", "completion": "0.00000015"}, "free": false, "free_tokens": 0}, {"id": "perplexity/sonar-reasoning", "name": "Perplexity: <PERSON><PERSON> Reasoning", "size": "Unknown", "context_length": 127000, "pricing": {"prompt": "0.000001", "completion": "0.000005"}, "free": false, "free_tokens": 0}, {"id": "perplexity/sonar", "name": "Perplexity: Sonar", "size": "Unknown", "context_length": 127072, "pricing": {"prompt": "0.000001", "completion": "0.000001"}, "free": false, "free_tokens": 0}, {"id": "liquid/lfm-7b", "name": "Liquid: LFM 7B", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.00000001", "completion": "0.00000001"}, "free": false, "free_tokens": 0}, {"id": "liquid/lfm-3b", "name": "Liquid: LFM 3B", "size": "3B", "context_length": 32768, "pricing": {"prompt": "0.00000002", "completion": "0.00000002"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-llama-70b:free", "name": "DeepSeek: R1 Distill Llama 70B (free)", "size": "70B", "context_length": 8192, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1-distill-llama-70b", "name": "DeepSeek: R1 Distill Llama 70B", "size": "70B", "context_length": 131072, "pricing": {"prompt": "0.0000001", "completion": "0.0000004"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1:free", "name": "DeepSeek: R1 (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-r1", "name": "DeepSeek: R1", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0.0000005", "completion": "0.00000218"}, "free": false, "free_tokens": 0}, {"id": "minimax/minimax-01", "name": "MiniMax: MiniMax-01", "size": "Unknown", "context_length": 1000192, "pricing": {"prompt": "0.0000002", "completion": "0.0000011"}, "free": false, "free_tokens": 0}, {"id": "mistralai/codestral-2501", "name": "Mistral: Codestral 2501", "size": "Unknown", "context_length": 262144, "pricing": {"prompt": "0.0000003", "completion": "0.0000009"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-4", "name": "Microsoft: Phi 4", "size": "Unknown", "context_length": 16384, "pricing": {"prompt": "0.00000007", "completion": "0.00000014"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-chat:free", "name": "DeepSeek: DeepSeek V3 (free)", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-chat", "name": "DeepSeek: DeepSeek V3", "size": "Unknown", "context_length": 163840, "pricing": {"prompt": "0.00000038", "completion": "0.00000089"}, "free": false, "free_tokens": 0}, {"id": "sao10k/l3.3-euryale-70b", "name": "Sao10K: Llama 3.3 Euryale 70B", "size": "70B", "context_length": 131072, "pricing": {"prompt": "0.0000007", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "openai/o1", "name": "OpenAI: o1", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000015", "completion": "0.00006"}, "free": false, "free_tokens": 0}, {"id": "eva-unit-01/eva-llama-3.33-70b", "name": "EVA Llama 3.33 70B", "size": "70B", "context_length": 16384, "pricing": {"prompt": "0.000004", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "x-ai/grok-2-vision-1212", "name": "xAI: Grok 2 Vision 1212", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.000002", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "x-ai/grok-2-1212", "name": "xAI: Grok 2 1212", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000002", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r7b-12-2024", "name": "Cohere: Command R7B (12-2024)", "size": "7B", "context_length": 128000, "pricing": {"prompt": "0.0000000375", "completion": "0.00000015"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-2.0-flash-exp:free", "name": "Google: Gemini 2.0 Flash Experimental (free)", "size": "Unknown", "context_length": 1048576, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.3-70b-instruct:free", "name": "Meta: Llama 3.3 70B Instruct (free)", "size": "70B", "context_length": 8000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.3-70b-instruct", "name": "Meta: Llama 3.3 70B Instruct", "size": "70B", "context_length": 128000, "pricing": {"prompt": "0.0000001", "completion": "0.00000025"}, "free": false, "free_tokens": 0}, {"id": "amazon/nova-lite-v1", "name": "Amazon: Nova Lite 1.0", "size": "Unknown", "context_length": 300000, "pricing": {"prompt": "0.00000006", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "amazon/nova-micro-v1", "name": "Amazon: Nova Micro 1.0", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000000035", "completion": "0.00000014"}, "free": false, "free_tokens": 0}, {"id": "amazon/nova-pro-v1", "name": "Amazon: Nova Pro 1.0", "size": "Unknown", "context_length": 300000, "pricing": {"prompt": "0.0000008", "completion": "0.0000032"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwq-32b-preview:free", "name": "Qwen: QwQ 32B Preview (free)", "size": "32B", "context_length": 16384, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwq-32b-preview", "name": "Qwen: QwQ 32B Preview", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0.00000009", "completion": "0.00000027"}, "free": false, "free_tokens": 0}, {"id": "google/learnlm-1.5-pro-experimental:free", "name": "Google: LearnLM 1.5 Pro Experimental (free)", "size": "Unknown", "context_length": 40960, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "eva-unit-01/eva-qwen-2.5-72b", "name": "EVA Qwen2.5 72B", "size": "72B", "context_length": 16384, "pricing": {"prompt": "0.000004", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-2024-11-20", "name": "OpenAI: GPT-4o (2024-11-20)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-large-2411", "name": "Mistral Large 2411", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000002", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-large-2407", "name": "Mistral Large 2407", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000002", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "mistralai/pixtral-large-2411", "name": "Mistral: Pixtral Large 2411", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000002", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "x-ai/grok-vision-beta", "name": "xAI: Grok Vision Beta", "size": "Unknown", "context_length": 8192, "pricing": {"prompt": "0.000005", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "infermatic/mn-inferor-12b", "name": "Infermatic: Mistral Nemo Inferor 12B", "size": "12B", "context_length": 16384, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-coder-32b-instruct:free", "name": "Qwen2.5 Coder 32B Instruct (free)", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-coder-32b-instruct", "name": "Qwen2.5 Coder 32B Instruct", "size": "32B", "context_length": 32768, "pricing": {"prompt": "0.00000006", "completion": "0.00000015"}, "free": false, "free_tokens": 0}, {"id": "raifle/sorcererlm-8x22b", "name": "SorcererLM 8x22B", "size": "2B", "context_length": 16000, "pricing": {"prompt": "0.0000045", "completion": "0.0000045"}, "free": false, "free_tokens": 0}, {"id": "eva-unit-01/eva-qwen-2.5-32b", "name": "EVA Qwen2.5 32B", "size": "32B", "context_length": 16384, "pricing": {"prompt": "0.0000026", "completion": "0.0000034"}, "free": false, "free_tokens": 0}, {"id": "thedrummer/unslopnemo-12b", "name": "Unslopnemo 12B", "size": "12B", "context_length": 32000, "pricing": {"prompt": "0.00000045", "completion": "0.00000045"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-haiku:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000008", "completion": "0.000004"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-haiku", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000008", "completion": "0.000004"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-haiku-20241022:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-10-22) (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000008", "completion": "0.000004"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-haiku-20241022", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-10-22)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.0000008", "completion": "0.000004"}, "free": false, "free_tokens": 0}, {"id": "neversleep/llama-3.1-lumimaid-70b", "name": "NeverSleep: Lumimaid v0.2 70B", "size": "70B", "context_length": 16384, "pricing": {"prompt": "0.0000015", "completion": "0.00000225"}, "free": false, "free_tokens": 0}, {"id": "anthracite-org/magnum-v4-72b", "name": "Magnum v4 72B", "size": "72B", "context_length": 16384, "pricing": {"prompt": "0.0000015", "completion": "0.00000225"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-sonnet:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-sonnet", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON>", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "x-ai/grok-beta", "name": "xAI: Grok Beta", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.000005", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "mistralai/ministral-8b", "name": "Mistral: Ministral 8B", "size": "8B", "context_length": 128000, "pricing": {"prompt": "0.0000001", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "mistralai/ministral-3b", "name": "Mistral: Ministral 3B", "size": "3B", "context_length": 131072, "pricing": {"prompt": "0.00000004", "completion": "0.00000004"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-7b-instruct:free", "name": "Qwen2.5 7B Instruct (free)", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-7b-instruct", "name": "Qwen2.5 7B Instruct", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.00000005", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "nvidia/llama-3.1-nemotron-70b-instruct", "name": "NVIDIA: Llama 3.1 Nemotron 70B Instruct", "size": "70B", "context_length": 131072, "pricing": {"prompt": "0.00000012", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "inflection/inflection-3-productivity", "name": "Inflection: Inflection 3 Productivity", "size": "Unknown", "context_length": 8000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "inflection/inflection-3-pi", "name": "Inflection: Inflection 3 Pi", "size": "Unknown", "context_length": 8000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-flash-1.5-8b", "name": "Google: Gemini 1.5 Flash 8B", "size": "8B", "context_length": 1000000, "pricing": {"prompt": "0.0000000375", "completion": "0.00000015"}, "free": false, "free_tokens": 0}, {"id": "thedrummer/rocinante-12b", "name": "Rocinante 12B", "size": "12B", "context_length": 32768, "pricing": {"prompt": "0.00000025", "completion": "0.0000005"}, "free": false, "free_tokens": 0}, {"id": "anthracite-org/magnum-v2-72b", "name": "Magnum v2 72B", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0.000003", "completion": "0.000003"}, "free": false, "free_tokens": 0}, {"id": "liquid/lfm-40b", "name": "Liquid: LFM 40B MoE", "size": "40B", "context_length": 32768, "pricing": {"prompt": "0.00000015", "completion": "0.00000015"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-3b-instruct:free", "name": "Meta: Llama 3.2 3B Instruct (free)", "size": "3B", "context_length": 20000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-3b-instruct", "name": "Meta: Llama 3.2 3B Instruct", "size": "3B", "context_length": 131072, "pricing": {"prompt": "0.00000001", "completion": "0.00000002"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-1b-instruct:free", "name": "Meta: Llama 3.2 1B Instruct (free)", "size": "1B", "context_length": 131000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-1b-instruct", "name": "Meta: Llama 3.2 1B Instruct", "size": "1B", "context_length": 131072, "pricing": {"prompt": "0.000000005", "completion": "0.00000001"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-90b-vision-instruct", "name": "Meta: Llama 3.2 90B Vision Instruct", "size": "90B", "context_length": 131072, "pricing": {"prompt": "0.0000012", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-11b-vision-instruct:free", "name": "Meta: Llama 3.2 11B Vision Instruct (free)", "size": "11B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.2-11b-vision-instruct", "name": "Meta: Llama 3.2 11B Vision Instruct", "size": "11B", "context_length": 131072, "pricing": {"prompt": "0.000000049", "completion": "0.000000049"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-72b-instruct:free", "name": "Qwen2.5 72B Instruct (free)", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-72b-instruct", "name": "Qwen2.5 72B Instruct", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0.00000012", "completion": "0.00000039"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-vl-72b-instruct", "name": "Qwen: Qwen2.5-VL 72B Instruct", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0.0000006", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "neversleep/llama-3.1-lumimaid-8b", "name": "NeverSleep: Lumimaid v0.2 8B", "size": "8B", "context_length": 32768, "pricing": {"prompt": "0.00000009375", "completion": "0.00000075"}, "free": false, "free_tokens": 0}, {"id": "openai/o1-preview", "name": "OpenAI: o1-preview", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000015", "completion": "0.00006"}, "free": false, "free_tokens": 0}, {"id": "openai/o1-preview-2024-09-12", "name": "OpenAI: o1-preview (2024-09-12)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000015", "completion": "0.00006"}, "free": false, "free_tokens": 0}, {"id": "openai/o1-mini", "name": "OpenAI: o1-mini", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000011", "completion": "0.0000044"}, "free": false, "free_tokens": 0}, {"id": "openai/o1-mini-2024-09-12", "name": "OpenAI: o1-mini (2024-09-12)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000011", "completion": "0.0000044"}, "free": false, "free_tokens": 0}, {"id": "mistralai/pixtral-12b", "name": "Mistral: Pixtral 12B", "size": "12B", "context_length": 32768, "pricing": {"prompt": "0.0000001", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r-plus-08-2024", "name": "Cohere: Command R+ (08-2024)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r-08-2024", "name": "Cohere: Command R (08-2024)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00000015", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-vl-7b-instruct:free", "name": "Qwen: Qwen2.5-VL 7B Instruct (free)", "size": "7B", "context_length": 64000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2.5-vl-7b-instruct", "name": "Qwen: Qwen2.5-VL 7B Instruct", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.0000002", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "sao10k/l3.1-euryale-70b", "name": "Sao10K: Llama 3.1 Euryale 70B v2.2", "size": "70B", "context_length": 131072, "pricing": {"prompt": "0.0000007", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-flash-1.5-8b-exp", "name": "Google: Gemini 1.5 Flash 8B Experimental", "size": "8B", "context_length": 1000000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-3.5-mini-128k-instruct", "name": "Microsoft: Phi-3.5 Mini 128K Instruct", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.00000003", "completion": "0.00000009"}, "free": false, "free_tokens": 0}, {"id": "nousresearch/hermes-3-llama-3.1-70b", "name": "Nous: Hermes 3 70B Instruct", "size": "70B", "context_length": 131072, "pricing": {"prompt": "0.00000012", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "nousresearch/hermes-3-llama-3.1-405b", "name": "Nous: Hermes 3 405B Instruct", "size": "5B", "context_length": 131072, "pricing": {"prompt": "0.0000008", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "openai/chatgpt-4o-latest", "name": "OpenAI: ChatGPT-4o", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000005", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "sao10k/l3-lunaris-8b", "name": "Sao10K: Llama 3 8B Lunaris", "size": "8B", "context_length": 8192, "pricing": {"prompt": "0.00000002", "completion": "0.00000005"}, "free": false, "free_tokens": 0}, {"id": "aetherwiing/mn-starcannon-12b", "name": "Aetherwiing: Starcannon 12B", "size": "12B", "context_length": 16384, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-2024-08-06", "name": "OpenAI: GPT-4o (2024-08-06)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.1-405b:free", "name": "Meta: Llama 3.1 405B (base) (free)", "size": "5B", "context_length": 64000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.1-405b", "name": "Meta: Llama 3.1 405B (base)", "size": "5B", "context_length": 32768, "pricing": {"prompt": "0.000002", "completion": "0.000002"}, "free": false, "free_tokens": 0}, {"id": "nothingiisreal/mn-celeste-12b", "name": "Mistral Nemo 12B Celeste", "size": "12B", "context_length": 16384, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "perplexity/llama-3.1-sonar-small-128k-online", "name": "Perplexity: Llama 3.1 Sonar 8B Online", "size": "Unknown", "context_length": 127072, "pricing": {"prompt": "0.0000002", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "perplexity/llama-3.1-sonar-large-128k-online", "name": "Perplexity: Llama 3.1 Sonar 70B Online", "size": "Unknown", "context_length": 127072, "pricing": {"prompt": "0.000001", "completion": "0.000001"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.1-8b-instruct:free", "name": "Meta: Llama 3.1 8B Instruct (free)", "size": "8B", "context_length": 131072, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.1-8b-instruct", "name": "Meta: Llama 3.1 8B Instruct", "size": "8B", "context_length": 16384, "pricing": {"prompt": "0.00000002", "completion": "0.00000003"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.1-405b-instruct", "name": "Meta: Llama 3.1 405B Instruct", "size": "5B", "context_length": 32768, "pricing": {"prompt": "0.0000008", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3.1-70b-instruct", "name": "Meta: Llama 3.1 70B Instruct", "size": "70B", "context_length": 131072, "pricing": {"prompt": "0.0000001", "completion": "0.00000028"}, "free": false, "free_tokens": 0}, {"id": "mistralai/codestral-mamba", "name": "Mistral: Codestral Mamba", "size": "Unknown", "context_length": 262144, "pricing": {"prompt": "0.00000025", "completion": "0.00000025"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-nemo:free", "name": "Mistral: <PERSON><PERSON><PERSON> (free)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-nemo", "name": "Mistral: <PERSON><PERSON><PERSON> Nemo", "size": "Unknown", "context_length": 98304, "pricing": {"prompt": "0.00000003", "completion": "0.00000007"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-mini", "name": "OpenAI: GPT-4o-mini", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00000015", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-mini-2024-07-18", "name": "OpenAI: GPT-4o-mini (2024-07-18)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00000015", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-2-27b-it", "name": "Google: Gemma 2 27B", "size": "7B", "context_length": 8192, "pricing": {"prompt": "0.0000001", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "alpindale/magnum-72b", "name": "Magnum 72B", "size": "72B", "context_length": 16384, "pricing": {"prompt": "0.000004", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-2-9b-it:free", "name": "Google: <PERSON> 2 9B (free)", "size": "9B", "context_length": 8192, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "google/gemma-2-9b-it", "name": "Google: Gemma 2 9B", "size": "9B", "context_length": 8192, "pricing": {"prompt": "0.00000002", "completion": "0.00000006"}, "free": false, "free_tokens": 0}, {"id": "01-ai/yi-large", "name": "01.AI: <PERSON>", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.000003", "completion": "0.000003"}, "free": false, "free_tokens": 0}, {"id": "ai21/jamba-instruct", "name": "AI21: Jamba Instruct", "size": "Unknown", "context_length": 256000, "pricing": {"prompt": "0.0000005", "completion": "0.0000007"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-sonnet-20240620:beta", "name": "Anthropic: <PERSON> 3.5 <PERSON> (2024-06-20) (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3.5-sonnet-20240620", "name": "Anthropic: <PERSON> 3.5 <PERSON><PERSON> (2024-06-20)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "sao10k/l3-euryale-70b", "name": "Sao10k: Llama 3 Euryale 70B v2.1", "size": "70B", "context_length": 8192, "pricing": {"prompt": "0.00000148", "completion": "0.00000148"}, "free": false, "free_tokens": 0}, {"id": "cognitivecomputations/dolphin-mixtral-8x22b", "name": "Dolphin 2.9.2 Mixtral 8x22B 🐬", "size": "2B", "context_length": 16000, "pricing": {"prompt": "0.0000009", "completion": "0.0000009"}, "free": false, "free_tokens": 0}, {"id": "qwen/qwen-2-72b-instruct", "name": "Qwen 2 72B Instruct", "size": "72B", "context_length": 32768, "pricing": {"prompt": "0.0000009", "completion": "0.0000009"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-7b-instruct:free", "name": "Mistral: Mistral 7B Instruct (free)", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0", "completion": "0"}, "free": false, "free_tokens": 0}, {"id": "mistral<PERSON>/mistral-7b-instruct", "name": "Mistral: Mistral 7B Instruct", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.000000028", "completion": "0.000000054"}, "free": false, "free_tokens": 0}, {"id": "nousresearch/hermes-2-pro-llama-3-8b", "name": "NousResearch: Hermes 2 Pro - Llama-3 8B", "size": "8B", "context_length": 131072, "pricing": {"prompt": "0.000000025", "completion": "0.00000004"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-7b-instruct-v0.3", "name": "Mistral: Mistral 7B Instruct v0.3", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.000000028", "completion": "0.000000054"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-3-mini-128k-instruct", "name": "Microsoft: Phi-3 Mini 128K Instruct", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000001", "completion": "0.0000001"}, "free": false, "free_tokens": 0}, {"id": "microsoft/phi-3-medium-128k-instruct", "name": "Microsoft: Phi-3 Medium 128K Instruct", "size": "Unknown", "context_length": 131072, "pricing": {"prompt": "0.0000001", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "neversleep/llama-3-lumimaid-70b", "name": "NeverSleep: Llama 3 Lumimaid 70B", "size": "70B", "context_length": 8192, "pricing": {"prompt": "0.000004", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "deepseek/deepseek-coder", "name": "DeepSeek-Coder-V2", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00000004", "completion": "0.00000012"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-flash-1.5", "name": "Google: Gemini 1.5 Flash ", "size": "Unknown", "context_length": 1000000, "pricing": {"prompt": "0.000000075", "completion": "0.0000003"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o", "name": "OpenAI: GPT-4o", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000025", "completion": "0.00001"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o:extended", "name": "OpenAI: GPT-4o (extended)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000006", "completion": "0.000018"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-guard-2-8b", "name": "Meta: LlamaGuard 2 8B", "size": "8B", "context_length": 8192, "pricing": {"prompt": "0.0000002", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4o-2024-05-13", "name": "OpenAI: GPT-4o (2024-05-13)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000005", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "allenai/olmo-7b-instruct", "name": "OLMo 7B Instruct", "size": "7B", "context_length": 2048, "pricing": {"prompt": "0.00000008", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "neversleep/llama-3-lumimaid-8b:extended", "name": "NeverSleep: Llama 3 Lumimaid 8B (extended)", "size": "8B", "context_length": 24576, "pricing": {"prompt": "0.00000009375", "completion": "0.00000075"}, "free": false, "free_tokens": 0}, {"id": "neversleep/llama-3-lumimaid-8b", "name": "NeverSleep: Llama 3 Lumimaid 8B", "size": "8B", "context_length": 24576, "pricing": {"prompt": "0.00000009375", "completion": "0.00000075"}, "free": false, "free_tokens": 0}, {"id": "sao10k/fimbulvetr-11b-v2", "name": "Fimbulvetr 11B v2", "size": "11B", "context_length": 4096, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3-8b-instruct", "name": "Meta: Llama 3 8B Instruct", "size": "8B", "context_length": 8192, "pricing": {"prompt": "0.00000003", "completion": "0.00000006"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-3-70b-instruct", "name": "Meta: Llama 3 70B Instruct", "size": "70B", "context_length": 8192, "pricing": {"prompt": "0.0000003", "completion": "0.0000004"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mixtral-8x22b-instruct", "name": "Mistral: Mixtral 8x22B Instruct", "size": "2B", "context_length": 65536, "pricing": {"prompt": "0.0000004", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "microsoft/wizardlm-2-8x22b", "name": "WizardLM-2 8x22B", "size": "2B", "context_length": 65536, "pricing": {"prompt": "0.0000005", "completion": "0.0000005"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-pro-1.5", "name": "Google: Gemini 1.5 Pro", "size": "Unknown", "context_length": 2000000, "pricing": {"prompt": "0.00000125", "completion": "0.000005"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4-turbo", "name": "OpenAI: GPT-4 Turbo", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00001", "completion": "0.00003"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r-plus", "name": "Cohere: Command R+", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r-plus-04-2024", "name": "Cohere: Command R+ (04-2024)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "sophosympatheia/midnight-rose-70b", "name": "<PERSON> Rose 70B", "size": "70B", "context_length": 4096, "pricing": {"prompt": "0.0000008", "completion": "0.0000008"}, "free": false, "free_tokens": 0}, {"id": "cohere/command", "name": "Cohere: Command", "size": "Unknown", "context_length": 4096, "pricing": {"prompt": "0.000001", "completion": "0.000002"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r", "name": "Cohere: Command R", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000005", "completion": "0.0000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3-haiku:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.00000025", "completion": "0.00000125"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3-haiku", "name": "Anthropic: <PERSON> 3 <PERSON><PERSON>", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.00000025", "completion": "0.00000125"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3-opus:beta", "name": "Anthropic: <PERSON> 3 <PERSON> (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000015", "completion": "0.000075"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3-opus", "name": "Anthropic: <PERSON> 3 Opus", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000015", "completion": "0.000075"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3-sonnet:beta", "name": "Anthropic: <PERSON> (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-3-sonnet", "name": "Anthropic: <PERSON> 3 <PERSON>", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000003", "completion": "0.000015"}, "free": false, "free_tokens": 0}, {"id": "cohere/command-r-03-2024", "name": "Cohere: Command R (03-2024)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.0000005", "completion": "0.0000015"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-large", "name": "Mistral Large", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.000002", "completion": "0.000006"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-3.5-turbo-0613", "name": "OpenAI: GPT-3.5 Turbo (older v0613)", "size": "Unknown", "context_length": 4095, "pricing": {"prompt": "0.000001", "completion": "0.000002"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4-turbo-preview", "name": "OpenAI: GPT-4 Turbo Preview", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00001", "completion": "0.00003"}, "free": false, "free_tokens": 0}, {"id": "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "name": "Nous: Hermes 2 Mixtral 8x7B DPO", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.0000006", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-medium", "name": "Mistral Medium", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.00000275", "completion": "0.0000081"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-small", "name": "Mistra<PERSON> Small", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.0000002", "completion": "0.0000006"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-tiny", "name": "Mistral Tiny", "size": "Unknown", "context_length": 32768, "pricing": {"prompt": "0.00000025", "completion": "0.00000025"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-7b-instruct-v0.2", "name": "Mistral: Mistral 7B Instruct v0.2", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.0000002", "completion": "0.0000002"}, "free": false, "free_tokens": 0}, {"id": "google/gemini-pro-vision", "name": "Google: Gemini Pro Vision 1.0", "size": "Unknown", "context_length": 16384, "pricing": {"prompt": "0.0000005", "completion": "0.0000015"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mixtral-8x7b-instruct", "name": "Mistral: Mixtral 8x7B Instruct", "size": "7B", "context_length": 32768, "pricing": {"prompt": "0.00000008", "completion": "0.00000024"}, "free": false, "free_tokens": 0}, {"id": "neversleep/noromaid-20b", "name": "Noromaid 20B", "size": "20B", "context_length": 8192, "pricing": {"prompt": "0.00000075", "completion": "0.0000015"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-2.1:beta", "name": "Anthropic: <PERSON> v2.1 (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000008", "completion": "0.000024"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-2.1", "name": "Anthropic: <PERSON> v2.1", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000008", "completion": "0.000024"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-2:beta", "name": "Anthropic: <PERSON> v2 (self-moderated)", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000008", "completion": "0.000024"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-2", "name": "Anthropic: <PERSON> v2", "size": "Unknown", "context_length": 200000, "pricing": {"prompt": "0.000008", "completion": "0.000024"}, "free": false, "free_tokens": 0}, {"id": "undi95/toppy-m-7b", "name": "Toppy M 7B", "size": "7B", "context_length": 4096, "pricing": {"prompt": "0.0000008", "completion": "0.0000012"}, "free": false, "free_tokens": 0}, {"id": "alpindale/goliath-120b", "name": "Goliath 120B", "size": "20B", "context_length": 6144, "pricing": {"prompt": "0.0000065625", "completion": "0.000009375"}, "free": false, "free_tokens": 0}, {"id": "openrouter/auto", "name": "Auto Router", "size": "Unknown", "context_length": 2000000, "pricing": {"prompt": "-1", "completion": "-1"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-3.5-turbo-1106", "name": "OpenAI: GPT-3.5 Turbo 16k (older v1106)", "size": "Unknown", "context_length": 16385, "pricing": {"prompt": "0.000001", "completion": "0.000002"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4-1106-preview", "name": "OpenAI: GPT-4 Turbo (older v1106)", "size": "Unknown", "context_length": 128000, "pricing": {"prompt": "0.00001", "completion": "0.00003"}, "free": false, "free_tokens": 0}, {"id": "jondurbin/airoboros-l2-70b", "name": "Airoboros 70B", "size": "70B", "context_length": 4096, "pricing": {"prompt": "0.0000005", "completion": "0.0000005"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-3.5-turbo-instruct", "name": "OpenAI: GPT-3.5 Turbo Instruct", "size": "Unknown", "context_length": 4095, "pricing": {"prompt": "0.0000015", "completion": "0.000002"}, "free": false, "free_tokens": 0}, {"id": "mistralai/mistral-7b-instruct-v0.1", "name": "Mistral: Mistral 7B Instruct v0.1", "size": "7B", "context_length": 2824, "pricing": {"prompt": "0.00000011", "completion": "0.00000019"}, "free": false, "free_tokens": 0}, {"id": "pygmalionai/mythalion-13b", "name": "Pygmalion: Mythalion 13B", "size": "13B", "context_length": 8192, "pricing": {"prompt": "0.0000005625", "completion": "0.000001125"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-3.5-turbo-16k", "name": "OpenAI: GPT-3.5 Turbo 16k", "size": "Unknown", "context_length": 16385, "pricing": {"prompt": "0.000003", "completion": "0.000004"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4-32k", "name": "OpenAI: GPT-4 32k", "size": "Unknown", "context_length": 32767, "pricing": {"prompt": "0.00006", "completion": "0.00012"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4-32k-0314", "name": "OpenAI: GPT-4 32k (older v0314)", "size": "Unknown", "context_length": 32767, "pricing": {"prompt": "0.00006", "completion": "0.00012"}, "free": false, "free_tokens": 0}, {"id": "mancer/weaver", "name": "Mancer: <PERSON> (alpha)", "size": "Unknown", "context_length": 8000, "pricing": {"prompt": "0.000001125", "completion": "0.000001125"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-2.0:beta", "name": "Anthropic: <PERSON> v2.0 (self-moderated)", "size": "Unknown", "context_length": 100000, "pricing": {"prompt": "0.000008", "completion": "0.000024"}, "free": false, "free_tokens": 0}, {"id": "anthropic/claude-2.0", "name": "Anthropic: <PERSON> v2.0", "size": "Unknown", "context_length": 100000, "pricing": {"prompt": "0.000008", "completion": "0.000024"}, "free": false, "free_tokens": 0}, {"id": "undi95/remm-slerp-l2-13b", "name": "ReMM SLERP 13B", "size": "13B", "context_length": 6144, "pricing": {"prompt": "0.0000005625", "completion": "0.000001125"}, "free": false, "free_tokens": 0}, {"id": "gryphe/mythomax-l2-13b", "name": "MythoMax 13B", "size": "13B", "context_length": 4096, "pricing": {"prompt": "0.000000065", "completion": "0.000000065"}, "free": false, "free_tokens": 0}, {"id": "meta-llama/llama-2-70b-chat", "name": "Meta: Llama 2 70B Chat", "size": "70B", "context_length": 4096, "pricing": {"prompt": "0.0000009", "completion": "0.0000009"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-3.5-turbo", "name": "OpenAI: GPT-3.5 Turbo", "size": "Unknown", "context_length": 16385, "pricing": {"prompt": "0.0000005", "completion": "0.0000015"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-3.5-turbo-0125", "name": "OpenAI: GPT-3.5 Turbo 16k", "size": "Unknown", "context_length": 16385, "pricing": {"prompt": "0.0000005", "completion": "0.0000015"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4", "name": "OpenAI: GPT-4", "size": "Unknown", "context_length": 8191, "pricing": {"prompt": "0.00003", "completion": "0.00006"}, "free": false, "free_tokens": 0}, {"id": "openai/gpt-4-0314", "name": "OpenAI: GPT-4 (older v0314)", "size": "Unknown", "context_length": 8191, "pricing": {"prompt": "0.00003", "completion": "0.00006"}, "free": false, "free_tokens": 0}]