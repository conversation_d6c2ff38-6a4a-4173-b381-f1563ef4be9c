name: Release

on:
  push:
    tags:
      - "v*"

permissions:
  contents: write
  packages: write

jobs:
  build-and-release:
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false

  build-linux-x86:
    needs: build-and-release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: x86_64-unknown-linux-gnu
          override: true
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: linux-x86_64-cargo-${{ hashFiles('**/Cargo.lock') }}
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release --target x86_64-unknown-linux-gnu
      - name: Prepare binary
        run: |
          mkdir -p dist
          cp ./target/x86_64-unknown-linux-gnu/release/aicommit ./dist/aicommit-linux-x86_64
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist/aicommit-linux-x86_64
          if-no-files-found: error
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-and-release.outputs.upload_url }}
          asset_path: ./dist/aicommit-linux-x86_64
          asset_name: aicommit-linux-x86_64
          asset_content_type: application/octet-stream

  build-macos-x86:
    needs: build-and-release
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: x86_64-apple-darwin
          override: true
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release --target x86_64-apple-darwin
      - name: Prepare binary
        run: |
          mkdir -p dist
          cp ./target/x86_64-apple-darwin/release/aicommit ./dist/aicommit-macos-x86_64
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: binaries-macos-x86_64-${{ github.ref_name }}
          path: ./dist/aicommit-macos-x86_64
          if-no-files-found: error
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-and-release.outputs.upload_url }}
          asset_path: ./dist/aicommit-macos-x86_64
          asset_name: aicommit-macos-x86_64
          asset_content_type: application/octet-stream

  build-macos-arm:
    needs: build-and-release
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: aarch64-apple-darwin
          override: true
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release --target aarch64-apple-darwin
      - name: Prepare binary
        run: |
          mkdir -p dist
          cp ./target/aarch64-apple-darwin/release/aicommit ./dist/aicommit-macos-aarch64
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: binaries-macos-aarch64-${{ github.ref_name }}
          path: ./dist/aicommit-macos-aarch64
          if-no-files-found: error
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-and-release.outputs.upload_url }}
          asset_path: ./dist/aicommit-macos-aarch64
          asset_name: aicommit-macos-aarch64
          asset_content_type: application/octet-stream

  build-windows-x86:
    needs: build-and-release
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: x86_64-pc-windows-msvc
          override: true
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release --target x86_64-pc-windows-msvc
      - name: Prepare binary
        run: |
          mkdir -p dist
          cp ./target/x86_64-pc-windows-msvc/release/aicommit.exe ./dist/aicommit-windows-x86_64.exe
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: binaries-windows-x86_64-${{ github.ref_name }}
          path: ./dist/aicommit-windows-x86_64.exe
          if-no-files-found: error
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-and-release.outputs.upload_url }}
          asset_path: ./dist/aicommit-windows-x86_64.exe
          asset_name: aicommit-windows-x86_64.exe
          asset_content_type: application/octet-stream

  build-windows-arm:
    needs: build-and-release
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: aarch64-pc-windows-msvc
          override: true
      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --release --target aarch64-pc-windows-msvc
      - name: Prepare binary
        run: |
          mkdir -p dist
          cp ./target/aarch64-pc-windows-msvc/release/aicommit.exe ./dist/aicommit-windows-aarch64.exe
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: binaries-windows-aarch64-${{ github.ref_name }}
          path: ./dist/aicommit-windows-aarch64.exe
          if-no-files-found: error
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-and-release.outputs.upload_url }}
          asset_path: ./dist/aicommit-windows-aarch64.exe
          asset_name: aicommit-windows-aarch64.exe
          asset_content_type: application/octet-stream

  publish-crates:
    needs: build-and-release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
      - name: Publish to crates.io
        uses: actions-rs/cargo@v1
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
        with:
          command: publish
          args: --allow-dirty

  publish-npm:
    needs: [build-linux-x86, build-macos-x86, build-macos-arm, build-windows-x86, build-windows-arm]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: binaries-*-${{ github.ref_name }}
          path: bin
          merge-multiple: true
      - name: Show binary files
        run: |
          echo "Binary files in bin/:"
          ls -la bin/
          # Проверяем наличие всех необходимых бинарников
          for file in aicommit-linux-x86_64 aicommit-macos-x86_64 aicommit-macos-aarch64 aicommit-windows-x86_64.exe aicommit-windows-aarch64.exe; do
            if [ ! -f "bin/$file" ]; then
              echo "Error: Missing binary: $file"
              exit 1
            fi
          done
          file bin/* || true
          chmod +x bin/aicommit-*
      - name: Rename binaries for npm
        run: |
          mkdir -p bin/linux-x64
          mkdir -p bin/darwin-x64
          mkdir -p bin/darwin-arm64
          mkdir -p bin/win32-x64
          mkdir -p bin/win32-arm64
          
          mv bin/aicommit-linux-x86_64 bin/linux-x64/aicommit
          mv bin/aicommit-macos-x86_64 bin/darwin-x64/aicommit
          mv bin/aicommit-macos-aarch64 bin/darwin-arm64/aicommit
          mv bin/aicommit-windows-x86_64.exe bin/win32-x64/aicommit.exe
          mv bin/aicommit-windows-aarch64.exe bin/win32-arm64/aicommit.exe
          
          chmod +x bin/**/aicommit*
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          registry-url: 'https://registry.npmjs.org'
      - name: Publish to npm
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-homebrew:
    needs: [build-macos-x86, build-macos-arm]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout tap repo
        uses: actions/checkout@v4
        with:
          repository: suenot/homebrew-tap
          path: homebrew-tap
          token: ${{ secrets.GH_TOKEN }}
      
      - name: Download macOS binaries
        uses: actions/download-artifact@v4
        with:
          pattern: binaries-macos-*
          path: dist/
          merge-multiple: true

      - name: Calculate SHA256
        id: sha-calc
        run: |
          echo "sha256_x86=$(sha256sum dist/aicommit-macos-x86_64 | awk '{ print $1 }')" >> $GITHUB_OUTPUT
          echo "sha256_arm=$(sha256sum dist/aicommit-macos-aarch64 | awk '{ print $1 }')" >> $GITHUB_OUTPUT

      - name: Update formula
        env:
          VERSION: ${{ github.ref_name }}
          SHA256_X86: ${{ steps.sha-calc.outputs.sha256_x86 }}
          SHA256_ARM: ${{ steps.sha-calc.outputs.sha256_arm }}
        run: |
          cat > homebrew-tap/Formula/aicommit.rb <<EOL
          class Aicommit < Formula
            desc "AI-powered commit message generator"
            homepage "https://github.com/suenot/aicommit"
            version "${VERSION}"
            license "MIT"

            on_macos do
              on_arm do
                url "https://github.com/suenot/aicommit/releases/download/${VERSION}/aicommit-macos-aarch64"
                sha256 "${SHA256_ARM}"

                def install
                  bin.install "aicommit-macos-aarch64" => "aicommit"
                end
              end

              on_intel do
                url "https://github.com/suenot/aicommit/releases/download/${VERSION}/aicommit-macos-x86_64"
                sha256 "${SHA256_X86}"

                def install
                  bin.install "aicommit-macos-x86_64" => "aicommit"
                end
              end
            end
          end
          EOL

      - name: Debug formula
        run: |
          echo "Generated formula:"
          cat homebrew-tap/Formula/aicommit.rb
          echo "Files in dist:"
          ls -la dist/

      - name: Commit and push
        run: |
          cd homebrew-tap
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git add Formula/aicommit.rb
          git commit -m "Update aicommit to ${{ github.ref_name }} with ARM support"
          git push

  build-deb-package:
    needs: [build-and-release, build-linux-x86]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Download Linux binary
        uses: actions/download-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist
      - name: Create .deb package
        run: |
          chmod +x ./dist/aicommit-linux-x86_64
          mkdir -p debian/DEBIAN debian/usr/bin
          cat > debian/DEBIAN/control << EOF
          Package: aicommit
          Version: ${GITHUB_REF#refs/tags/v}
          Section: utils
          Priority: optional
          Architecture: amd64
          Maintainer: Cascade Team
          Description: AI-powered commit message generator
           A tool that helps developers write better commit messages using AI.
          EOF
          cp ./dist/aicommit-linux-x86_64 debian/usr/bin/aicommit
          dpkg-deb --build debian ./dist/aicommit_${GITHUB_REF#refs/tags/v}_amd64.deb
      - name: Upload .deb artifact
        uses: actions/upload-artifact@v4
        with:
          name: aicommit-deb-${{ github.ref_name }}
          path: ./dist/aicommit_*_amd64.deb
          if-no-files-found: error
      - name: Upload .deb Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          UPLOAD_URL: ${{ needs.build-and-release.outputs.upload_url }}
        with:
          upload_url: ${{ env.UPLOAD_URL }}
          asset_path: ./dist/aicommit_${GITHUB_REF#refs/tags/v}_amd64.deb
          asset_name: aicommit_${{ github.ref_name }}_amd64.deb
          asset_content_type: application/vnd.debian.binary-package

  build-rpm-package:
    needs: [build-and-release, build-linux-x86]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install RPM tools
        run: sudo apt-get update && sudo apt-get install -y rpm
      - name: Download Linux binary
        uses: actions/download-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist
      - name: Create RPM structure
        run: |
          chmod +x ./dist/aicommit-linux-x86_64
          mkdir -p rpmbuild/{SPECS,BUILD,BUILDROOT,RPMS,SOURCES}
          cp ./dist/aicommit-linux-x86_64 rpmbuild/SOURCES/aicommit
          cat > rpmbuild/SPECS/aicommit.spec << EOF
          Name:           aicommit
          Version:        ${GITHUB_REF#refs/tags/v}
          Release:        1%{?dist}
          Summary:        AI-powered commit message generator
          License:        MIT
          URL:           https://github.com/suenot/aicommit
          BuildArch:     x86_64

          %description
          A tool that helps developers write better commit messages using AI.

          %install
          mkdir -p %{buildroot}/usr/bin
          cp %{_sourcedir}/aicommit %{buildroot}/usr/bin/aicommit

          %files
          /usr/bin/aicommit

          %changelog
          * $(date '+%a %b %d %Y') Cascade Team <<EMAIL>> - ${GITHUB_REF#refs/tags/v}
          - Initial RPM release
          EOF
      - name: Build RPM package
        run: rpmbuild --define "_topdir $(pwd)/rpmbuild" -bb rpmbuild/SPECS/aicommit.spec
      - name: Move RPM to dist
        run: |
          mkdir -p dist
          cp rpmbuild/RPMS/x86_64/*.rpm ./dist/
      - name: Upload RPM artifact
        uses: actions/upload-artifact@v4
        with:
          name: aicommit-rpm-${{ github.ref_name }}
          path: ./dist/*.rpm
          if-no-files-found: error
      - name: Upload RPM Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          UPLOAD_URL: ${{ needs.build-and-release.outputs.upload_url }}
        with:
          upload_url: ${{ env.UPLOAD_URL }}
          asset_path: ./dist/aicommit-${{ github.ref_name }}-1.x86_64.rpm
          asset_name: aicommit-${{ github.ref_name }}-1.x86_64.rpm
          asset_content_type: application/x-rpm

  build-alpine-package:
    needs: [build-and-release, build-linux-x86]
    runs-on: ubuntu-latest
    container: alpine:latest
    steps:
      - uses: actions/checkout@v4
      - name: Install build tools
        run: |
          apk add --no-cache alpine-sdk
      - name: Download Linux binary
        uses: actions/download-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist
      - name: Create APKBUILD
        run: |
          chmod +x ./dist/aicommit-linux-x86_64
          mkdir -p ~/packages/aicommit
          cat > ~/packages/aicommit/APKBUILD << EOF
          # Contributor: Cascade Team
          # Maintainer: Cascade Team
          pkgname=aicommit
          pkgver=${GITHUB_REF#refs/tags/v}
          pkgrel=0
          pkgdesc="AI-powered commit message generator"
          url="https://github.com/suenot/aicommit"
          arch="x86_64"
          license="MIT"
          depends=""
          makedepends=""
          source=""
          builddir="\$srcdir/\$pkgname-\$pkgver"

          package() {
              mkdir -p "\$pkgdir"/usr/bin
              install -m755 ../../dist/aicommit-linux-x86_64 "\$pkgdir"/usr/bin/aicommit
          }
          EOF
      - name: Build Alpine package
        run: |
          cd ~/packages/aicommit
          abuild-keygen -a -n
          abuild -r
      - name: Copy package to dist
        run: |
          mkdir -p dist
          cp ~/packages/aicommit/packages/x86_64/*.apk ./dist/
      - name: Upload Alpine package artifact
        uses: actions/upload-artifact@v4
        with:
          name: aicommit-alpine-${{ github.ref_name }}
          path: ./dist/*.apk
          if-no-files-found: error
      - name: Upload Alpine Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          UPLOAD_URL: ${{ needs.build-and-release.outputs.upload_url }}
        with:
          upload_url: ${{ env.UPLOAD_URL }}
          asset_path: ./dist/aicommit-${{ github.ref_name }}-r0.apk
          asset_name: aicommit-${{ github.ref_name }}-r0.apk
          asset_content_type: application/x-alpine-linux-package

  build-arch-package:
    needs: [build-and-release, build-linux-x86]
    runs-on: ubuntu-latest
    container: archlinux:base-devel
    steps:
      - uses: actions/checkout@v4
      - name: Download Linux binary
        uses: actions/download-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist
      - name: Create PKGBUILD
        run: |
          chmod +x ./dist/aicommit-linux-x86_64
          mkdir -p pkg
          cat > pkg/PKGBUILD << EOF
          # Maintainer: Cascade Team
          pkgname=aicommit
          pkgver=${GITHUB_REF#refs/tags/v}
          pkgrel=1
          pkgdesc="AI-powered commit message generator"
          arch=('x86_64')
          url="https://github.com/suenot/aicommit"
          license=('MIT')
          depends=()
          source=()
          sha256sums=()

          package() {
              install -Dm755 ../dist/aicommit-linux-x86_64 "\$pkgdir/usr/bin/aicommit"
          }
          EOF
      - name: Build Arch package
        run: |
          cd pkg
          makepkg -f
      - name: Copy package to dist
        run: |
          mkdir -p dist
          cp pkg/*.pkg.tar.zst ./dist/
      - name: Upload Arch package artifact
        uses: actions/upload-artifact@v4
        with:
          name: aicommit-arch-${{ github.ref_name }}
          path: ./dist/*.pkg.tar.zst
          if-no-files-found: error
      - name: Upload Arch Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          UPLOAD_URL: ${{ needs.build-and-release.outputs.upload_url }}
        with:
          upload_url: ${{ env.UPLOAD_URL }}
          asset_path: ./dist/aicommit-${{ github.ref_name }}-1-x86_64.pkg.tar.zst
          asset_name: aicommit-${{ github.ref_name }}-1-x86_64.pkg.tar.zst
          asset_content_type: application/zstd

  build-snap-package:
    needs: [build-and-release, build-linux-x86]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install Snapcraft
        run: |
          sudo snap install snapcraft --classic
          sudo snap install lxd
          sudo lxd init --auto
      - name: Download Linux binary
        uses: actions/download-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist
      - name: Create snapcraft.yaml
        run: |
          mkdir -p snap
          cat > snap/snapcraft.yaml << EOF
          name: aicommit
          version: '${GITHUB_REF#refs/tags/v}'
          summary: AI-powered commit message generator
          description: |
            A tool that helps developers write better commit messages using AI.
          grade: stable
          confinement: strict
          base: core22
          
          apps:
            aicommit:
              command: bin/aicommit
              plugs:
                - home
                - network
                - removable-media
          
          parts:
            aicommit:
              plugin: dump
              source: dist/
              prime:
                - aicommit-linux-x86_64
              override-prime: |
                craftctl default
                mv aicommit-linux-x86_64 bin/aicommit
                chmod +x bin/aicommit
          EOF
      - name: Build Snap package
        run: |
          sudo chmod a+w .
          snapcraft --destructive-mode
      - name: Upload Snap artifact
        uses: actions/upload-artifact@v4
        with:
          name: aicommit-snap-${{ github.ref_name }}
          path: ./*.snap
          if-no-files-found: error
      - name: Upload Snap Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          UPLOAD_URL: ${{ needs.build-and-release.outputs.upload_url }}
        with:
          upload_url: ${{ env.UPLOAD_URL }}
          asset_path: ./aicommit_${{ github.ref_name }}_amd64.snap
          asset_name: aicommit_${{ github.ref_name }}_amd64.snap
          asset_content_type: application/vnd.snap

  build-nix-package:
    needs: [build-and-release, build-linux-x86]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install Nix
        uses: cachix/install-nix-action@v24
      - name: Download Linux binary
        uses: actions/download-artifact@v4
        with:
          name: binaries-linux-x86_64-${{ github.ref_name }}
          path: ./dist
      - name: Create default.nix
        run: |
          cat > default.nix << EOF
          { lib, stdenv }:
          
          stdenv.mkDerivation rec {
            pname = "aicommit";
            version = "${GITHUB_REF#refs/tags/v}";
          
            src = ./dist;
          
            dontUnpack = true;
            dontBuild = true;
          
            installPhase = ''
              mkdir -p \$out/bin
              cp \$src/aicommit-linux-x86_64 \$out/bin/aicommit
              chmod +x \$out/bin/aicommit
            '';
          
            meta = with lib; {
              description = "AI-powered commit message generator";
              homepage = "https://github.com/suenot/aicommit";
              license = licenses.mit;
              maintainers = [];
              platforms = platforms.linux;
            };
          }
          EOF
      - name: Create flake.nix
        run: |
          cat > flake.nix << EOF
          {
            description = "AI-powered commit message generator";
          
            inputs = {
              nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
              flake-utils.url = "github:numtide/flake-utils";
            };
          
            outputs = { self, nixpkgs, flake-utils }:
              flake-utils.lib.eachDefaultSystem (system:
                let
                  pkgs = nixpkgs.legacyPackages.\${system};
                in
                {
                  packages = rec {
                    aicommit = pkgs.callPackage ./default.nix {};
                    default = aicommit;
                  };
                }
              );
          }
          EOF
      - name: Build Nix package
        run: |
          nix build
          mkdir -p dist-nix
          cp result dist-nix/aicommit-${{ github.ref_name }}.tar.gz
      - name: Upload Nix artifact
        uses: actions/upload-artifact@v4
        with:
          name: aicommit-nix-${{ github.ref_name }}
          path: dist-nix/*.tar.gz
          if-no-files-found: error
      - name: Upload Nix Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          UPLOAD_URL: ${{ needs.build-and-release.outputs.upload_url }}
        with:
          upload_url: ${{ env.UPLOAD_URL }}
          asset_path: ./dist-nix/aicommit-${{ github.ref_name }}.tar.gz
          asset_name: aicommit-${{ github.ref_name }}.tar.gz
          asset_content_type: application/gzip