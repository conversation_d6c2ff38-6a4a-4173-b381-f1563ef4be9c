:root {
    --primary-color: #6366f1;
    --secondary-color: #4f46e5;
    --background-color: #ffffff;
    --text-color: #1f2937;
    --gray-light: #f3f4f6;
    --gray-medium: #9ca3af;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
nav {
    background-color: var(--background-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo img {
    height: 32px;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
}

.github-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Hero Section */
.hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(to bottom right, #f0f7ff, #e0e7ff);
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.25rem;
    color: #4b5563;
    margin-bottom: 2rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
}

.primary {
    background-color: var(--primary-color);
    color: white;
}

.secondary {
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    margin-left: 1rem;
}

.cta-buttons {
    margin-top: 2rem;
}

/* Features Section */
.features {
    padding: 4rem 0;
    background-color: var(--gray-light);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.feature-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Installation Section */
.installation {
    padding: 4rem 0;
}

.install-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.install-card {
    background: var(--gray-light);
    padding: 2rem;
    border-radius: 0.5rem;
}

pre {
    background: #1a1a1a;
    color: #fff;
    padding: 1rem;
    border-radius: 0.25rem;
    overflow-x: auto;
    margin-top: 1rem;
}

/* Usage Section */
.usage {
    padding: 4rem 0;
    background-color: var(--gray-light);
}

.usage-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.step {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.step-number {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 2rem;
    margin-bottom: 1rem;
}

/* Footer */
footer {
    background: var(--text-color);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

footer .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .feature-grid,
    .install-options,
    .usage-steps {
        grid-template-columns: 1fr;
    }
    
    footer .container {
        flex-direction: column;
        gap: 1rem;
    }
}