<?xml version="1.0" encoding="UTF-8"?>
<svg width="116" height="20" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <linearGradient id="b" x2="0" y2="100%">
    <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
    <stop offset="1" stop-opacity=".1"/>
  </linearGradient>
  <mask id="a">
    <rect width="116" height="20" rx="3" fill="#fff"/>
  </mask>
  <g mask="url(#a)">
    <path fill="#555" d="M0 0h71v20H0z"/>
    <path fill="#007acc" d="M71 0h45v20H71z"/>
    <path fill="url(#b)" d="M0 0h116v20H0z"/>
  </g>
  <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
    <text x="35.5" y="15" fill="#010101" fill-opacity=".3">VS Code</text>
    <text x="35.5" y="14">VS Code</text>
    <text x="92.5" y="15" fill="#010101" fill-opacity=".3">ready</text>
    <text x="92.5" y="14">ready</text>
  </g>
</svg>
