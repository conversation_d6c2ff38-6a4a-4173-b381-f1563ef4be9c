{"author": "<PERSON><PERSON>", "bin": {"aicommit": "./index.js"}, "bugs": {"url": "https://github.com/suenot/aicommit/issues"}, "cpu": ["x64", "arm64"], "description": "A CLI tool that generates concise and descriptive git commit messages using LLM", "directories": {"doc": "docs", "test": "tests"}, "files": ["index.js", "bin/*", "scripts/*"], "homepage": "https://github.com/suenot/aicommit#readme", "keywords": ["git", "ai", "version", "commit", "autocommit", "llm", "aicommit"], "license": "MIT", "main": "index.js", "name": "@suenot/aicommit", "os": ["darwin", "linux", "win32"], "repository": {"type": "git", "url": "git+https://github.com/suenot/aicommit.git"}, "scripts": {"docs:build": "vitepress build docs", "docs:dev": "vitepress dev docs", "docs:preview": "vitepress preview docs", "install-globally": "cargo install --path .", "new-version": "aicommit --add --version-file version --version-iterate --version-cargo --version-npm --version-github --push", "postinstall": "chmod +x index.js", "start": "node index.js", "vscode:build": "cd vscode-extension && bash build.sh", "vscode:install": "cd vscode-extension && code --install-extension aicommit-vscode-0.1.0.vsix", "vscode:package": "cd vscode-extension && vsce package"}, "version": "0.1.139"}