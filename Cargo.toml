[package]
name = "aicommit"
version = "0.1.139"
edition = "2021"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
description = "A CLI tool that generates concise and descriptive git commit messages using LLMs"
readme = "readme.md"
repository = "https://github.com/suenot/aicommit"
license = "MIT"
keywords = ["git", "commit", "ai", "llm", "cli"]
categories = ["command-line-utilities", "development-tools", "development-tools::build-utils"]

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "native-tls"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
dialoguer = "0.11"
console = "0.15"
dirs = "5.0"
uuid = { version = "1.6", features = ["v4", "serde"] }
dotenv = "0.15"
clap = { version = "4.4", features = ["derive"] }
regex = "1.5"
log = "0.4"
env_logger = "0.10"
lazy_static = "1.4.0"
chrono = { version = "0.4", features = ["serde"] }